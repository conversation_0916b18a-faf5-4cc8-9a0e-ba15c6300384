//
//  EmptyStateView.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/15.
//  Updated by <PERSON><PERSON> on 2025/1/19 - Enhanced with brand colors and error states
//

import SwiftUI

// MARK: - Empty State Types

enum EmptyStateType: Equatable {
    case ready
    case listening
    case connecting
    case noInternet
    case noPermission
    case error(String)
}

// MARK: - Empty State View

struct EmptyStateView: View {
    // MARK: - Properties
    
    let type: EmptyStateType
    @State private var breathingScale: CGFloat = 1.0
    @State private var particleOffset: CGFloat = 0
    @State private var instructionOpacity: Double = 0.0
    
    // MARK: - Computed Properties
    
    private var mainIcon: String {
        switch type {
        case .ready:
            return "waveform"
        case .listening:
            return "waveform"
        case .connecting:
            return "antenna.radiowaves.left.and.right"
        case .noInternet:
            return "wifi.exclamationmark"
        case .noPermission:
            return "mic.slash"
        case .error:
            return "exclamationmark.triangle"
        }
    }
    
    private var mainTitle: String {
        switch type {
        case .ready:
            return "Ready to transcribe"
        case .listening:
            return "Listening..."
        case .connecting:
            return "Connecting to server..."
        case .noInternet:
            return "No Internet Connection"
        case .noPermission:
            return "Microphone Access Required"
        case .error:
            return "Connection Error"
        }
    }
    
    private var subtitle: String {
        switch type {
        case .ready:
            return "Tap the record button to start"
        case .listening:
            return "Speak clearly into your microphone"
        case .connecting:
            return "Establishing connection to transcription service"
        case .noInternet:
            return "Please check your internet connection and try again"
        case .noPermission:
            return "Please enable microphone access in Settings to use voice transcription"
        case .error(let message):
            return message
        }
    }
    
    private var callToAction: String? {
        switch type {
        case .ready:
            return nil
        case .listening:
            return nil
        case .connecting:
            return nil
        case .noInternet:
            return "Retry Connection"
        case .noPermission:
            return "Open Settings"
        case .error:
            return "Retry Connection"
        }
    }
    
    private var iconColor: Color {
        switch type {
        case .ready, .listening, .connecting:
            return .brandOrchid
        case .noInternet:
            return .brandAmber
        case .noPermission:
            return .brandOrchid.opacity(1)
        case .error:
            return .brandAmber
        }
    }
    
    private var backgroundColor: Color {
        return .clear
    }
    
    // MARK: - Body
    
    var body: some View {
        ZStack {
            // Background with brand colors
            backgroundColor
                .ignoresSafeArea()
            
            // Subtle background animations
            backgroundAnimations
            
            // Main content
            VStack(spacing: DesignSystem.spacing.xLarge) {
                // Animated icon section
                iconSection
                
                // Text content with progressive disclosure
                textContent
                
                // Call to action (if applicable)
                if let cta = callToAction {
                    callToActionButton(cta)
                }
                
                // Listening indicator (only when listening)
                if case .listening = type {
                    listeningIndicator
                }
            }
            .padding(DesignSystem.spacing.large)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onAppear {
            startAnimations()
        }
    }
    
    // MARK: - Subviews
    
    private var backgroundAnimations: some View {
        GeometryReader { geometry in
            ZStack {
                // Subtle particle animation background with brand colors - centered in available space
                ForEach(0..<5, id: \.self) { index in
                    Circle()
                        .fill(Color.brandFrenchLilac.opacity(0.1))
                        .frame(width: 20 + CGFloat(index * 10), height: 20 + CGFloat(index * 10))
                        .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                        .offset(
                            x: particleOffset * CGFloat(index + 1) * 0.3,
                            y: particleOffset * CGFloat(index + 1) * 0.2
                        )
                        .animation(
                            .easeInOut(duration: Double(3 + index))
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.5),
                            value: particleOffset
                        )
                }
            }
        }
    }
    
    private var iconSection: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            ZStack {
                // Background circle with brand gradient
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.brandFrenchLilac.opacity(0.3),
                                Color.brandOrchid.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 120, height: 120)
                    .scaleEffect(breathingScale)
                    .animation(
                        DesignSystem.animations.breathingEffect,
                        value: breathingScale
                    )
                
                // Main animated icon
                Image(systemName: mainIcon)
                    .font(.system(size: 48, weight: .light))
                    .foregroundColor(iconColor)
                    .scaleEffect(breathingScale)
                    .symbolEffect(.pulse, isActive: type == .listening)
                    .animation(
                        DesignSystem.animations.spring.delay(0.2),
                        value: breathingScale
                    )
            }
            
            // Connection status indicator (when connecting)
            if case .connecting = type {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: iconColor))
                    .scaleEffect(0.8)
            }
        }
    }
    
    private var textContent: some View {
        VStack(spacing: DesignSystem.spacing.medium) {
            // Main title with brand colors
            Text(mainTitle)
                .font(DesignSystem.typography.title2)
                .fontWeight(.semibold)
                .foregroundColor(.brandPersianPurple)
                .multilineTextAlignment(.center)
                .opacity(instructionOpacity)
                .animation(
                    .easeInOut(duration: 0.8)
                    .delay(0.3),
                    value: instructionOpacity
                )
            
            // Subtitle with progressive disclosure
            Text(subtitle)
                .font(DesignSystem.typography.bodySecondary)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                .opacity(instructionOpacity)
                .animation(
                    .easeInOut(duration: 0.8)
                    .delay(0.6),
                    value: instructionOpacity
                )
        }
    }
    
    private func callToActionButton(_ title: String) -> some View {
        BrandButton(
            title,
            style: type == .error("") ? .primary : .secondary,
            size: .small
        ) {
            HapticPattern.light.trigger()
            handleCallToAction()
        }
        .opacity(instructionOpacity)
        .animation(
            .easeInOut(duration: 0.8)
            .delay(0.9),
            value: instructionOpacity
        )
    }
    
    private var listeningIndicator: some View {
        HStack(spacing: DesignSystem.spacing.small) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(Color.brandOrchid)
                    .frame(width: 8, height: 8)
                    .scaleEffect(breathingScale)
                    .animation(
                        .easeInOut(duration: 0.6)
                        .repeatForever(autoreverses: true)
                        .delay(Double(index) * 0.2),
                        value: breathingScale
                    )
            }
        }
        .opacity(instructionOpacity)
        .animation(
            .easeInOut(duration: 0.8)
            .delay(1.2),
            value: instructionOpacity
        )
    }
    
    // MARK: - Animation Control
    
    private func startAnimations() {
        // Start breathing animation
        withAnimation {
            breathingScale = type == .listening ? 1.1 : 1.05
        }
        
        // Start particle animation
        withAnimation {
            particleOffset = 30
        }
        
        // Progressive disclosure of instructions
        withAnimation {
            instructionOpacity = 1.0
        }
    }
    
    // MARK: - Actions
    
    private func handleCallToAction() {
        switch type {
        case .noPermission:
            // Open Settings app
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(settingsUrl)
            }
        case .error:
            // Trigger retry action - this would be handled by parent view
            HapticPattern.light.trigger()
        case .ready:
            // Start recording - this would be handled by parent view
            HapticPattern.light.trigger()
        default:
            break
        }
    }
}

// MARK: - Convenience Initializers

extension EmptyStateView {
    /// Create a ready state empty view
    static var ready: EmptyStateView {
        EmptyStateView(type: .ready)
    }
    
    /// Create a listening state empty view
    static var listening: EmptyStateView {
        EmptyStateView(type: .listening)
    }
    
    /// Create a connecting state empty view
    static var connecting: EmptyStateView {
        EmptyStateView(type: .connecting)
    }
    
    /// Create a no permission state empty view
    static var noPermission: EmptyStateView {
        EmptyStateView(type: .noPermission)
    }
    
    /// Create an error state empty view
    static func error(_ message: String) -> EmptyStateView {
        EmptyStateView(type: .error(message))
    }
}

// MARK: - Preview

#if DEBUG
struct EmptyStateViewPreview: View {
    @State private var currentState: EmptyStateType = .ready
    
    var body: some View {
        VStack {
            EmptyStateView(type: currentState)
            
            // State switcher for preview
            HStack {
                Button("Ready") { currentState = .ready }
                Button("Listening") { currentState = .listening }
                Button("Connecting") { currentState = .connecting }
                Button("No Permission") { currentState = .noPermission }
                Button("Error") { currentState = .error("Connection failed") }
            }
            .padding()
        }
        .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
    }
}

#Preview {
    EmptyStateViewPreview()
}
#endif
