//
//  NetworkReachabilityManager.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/8/1.
//

import Foundation
import Network
import Combine
import SwiftUI

/// Network connectivity states
enum NetworkStatus: Equatable {
    case unknown
    case connected(ConnectionType)
    case disconnected
    
    enum ConnectionType {
        case wifi
        case cellular
        case ethernet
        case other
    }
    
    var isConnected: Bool {
        if case .connected = self {
            return true
        }
        return false
    }
}

/// Manages network reachability monitoring using iOS Network framework
class NetworkReachabilityManager: ObservableObject {
    
    // MARK: - Singleton
    
    static let shared = NetworkReachabilityManager()
    
    // MARK: - Published Properties
    
    @Published var networkStatus: NetworkStatus = .unknown
    @Published var isConnected: Bool = false
    
    // MARK: - Private Properties
    
    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkReachabilityManager")
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    private init() {
        setupNetworkMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// Start monitoring network connectivity
    func startMonitoring() {
        print("🌐 NetworkReachability: Starting network monitoring")
        monitor.start(queue: queue)

        // Get initial network status
        let currentPath = monitor.currentPath
        DispatchQueue.main.async { [weak self] in
            self?.updateNetworkStatus(currentPath)
            print("🌐 NetworkReachability: Initial network status: \(self?.networkStatus ?? .unknown)")
        }
    }
    
    /// Stop monitoring network connectivity
    func stopMonitoring() {
        print("🌐 NetworkReachability: Stopping network monitoring")
        monitor.cancel()
    }
    
    /// Check if network is available for WebSocket connections
    func isNetworkAvailableForWebSocket() -> Bool {
        let isAvailable = networkStatus.isConnected
        print("🌐 NetworkReachability: WebSocket availability check - Status: \(networkStatus), Available: \(isAvailable)")
        return isAvailable
    }
    
    /// Get current connection type description
    func getConnectionDescription() -> String {
        switch networkStatus {
        case .unknown:
            return "Unknown"
        case .connected(let type):
            switch type {
            case .wifi:
                return "Wi-Fi"
            case .cellular:
                return "Cellular"
            case .ethernet:
                return "Ethernet"
            case .other:
                return "Connected"
            }
        case .disconnected:
            return "No Connection"
        }
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path)
            }
        }
        
        // Update isConnected when networkStatus changes
        $networkStatus
            .map { $0.isConnected }
            .assign(to: \.isConnected, on: self)
            .store(in: &cancellables)
    }
    
    private func updateNetworkStatus(_ path: NWPath) {
        let previousStatus = networkStatus

        if path.status == .satisfied {
            let connectionType = determineConnectionType(path)
            networkStatus = .connected(connectionType)

            if previousStatus != networkStatus {
                print("🌐 NetworkReachability: Connected via \(getConnectionDescription())")
            }
        } else {
            networkStatus = .disconnected

            if previousStatus != networkStatus {
                print("🌐 NetworkReachability: Network disconnected")
            }
        }
    }
    
    private func determineConnectionType(_ path: NWPath) -> NetworkStatus.ConnectionType {
        if path.usesInterfaceType(.wifi) {
            return .wifi
        } else if path.usesInterfaceType(.cellular) {
            return .cellular
        } else if path.usesInterfaceType(.wiredEthernet) {
            return .ethernet
        } else {
            return .other
        }
    }
    
    deinit {
        stopMonitoring()
    }
}

// MARK: - SwiftUI Environment Integration

/// Environment key for NetworkReachabilityManager
struct NetworkReachabilityManagerKey: EnvironmentKey {
    static let defaultValue = NetworkReachabilityManager.shared
}

extension EnvironmentValues {
    var networkReachability: NetworkReachabilityManager {
        get { self[NetworkReachabilityManagerKey.self] }
        set { self[NetworkReachabilityManagerKey.self] = newValue }
    }
}
