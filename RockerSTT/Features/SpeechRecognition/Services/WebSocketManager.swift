//
//  WebSocketManager.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/6/28.
//

import Foundation
import Network
import Combine

protocol WebSocketManagerDelegate: AnyObject {
    func webSocketManager(_ manager: WebSocketManager, didReceiveTranscription transcription: TranscriptionResponse)
    func webSocketManager(_ manager: WebSocketManager, didChangeConnectionState state: WebSocketConnectionState)
    func webSocketManager(_ manager: WebSocketManager, didEncounterError error: Error)
}

enum WebSocketConnectionState: Equatable {
    case disconnected(reason: DisconnectionReason?)
    case connecting
    case connected
    case reconnecting(attempt: Int?, maxAttempts: Int?)

    var shouldShowError: Bool {
        switch self {
        case .disconnected(let reason):
            return reason != .userInitiated && reason != nil
        case .reconnecting:
            return false // Show status, not error
        default:
            return false
        }
    }
    
    func isReconnecting() -> Bool {
        if case .reconnecting = self {
            return true
        }
        return false
    }
    
    func isUserInitiated() -> Bool {
        if case  .disconnected(let reason) = self {
            if let reason = reason, reason != .userInitiated {
                return true
            }
        }
        return false
    }
}

// TranscriptionResponse is now defined in SpeechRecognitionViewModel.swift with FunASR format

class WebSocketManager: NSObject, ObservableObject {
    weak var delegate: WebSocketManagerDelegate?
    
    @Published var connectionState: WebSocketConnectionState = .disconnected(reason: nil)
    @Published var serverURL: String = "ws://rockerwww.ddns.net:10096/"
    
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?
    private var reconnectTimer: Timer?
    private var connectionTimeoutTimer: Timer?
    private var reconnectAttempts = 0
    private let maxReconnectAttempts = 5
    private let reconnectDelay: TimeInterval = 2.0
    private let connectionTimeout: TimeInterval = 10.0
    
    // FunASR Configuration (adapted from wsconnecter.js)
    private var funasrConfig: FunASRConfiguration?
    private var isConfigurationSent = false
    
    // Disconnection reason tracking properties
    private var disconnectionReason: DisconnectionReason = .unknown
    private var isUserInitiatedDisconnection = false

    // Network reachability monitoring
    private let networkReachability = NetworkReachabilityManager.shared
    private var cancellables = Set<AnyCancellable>()

    override init() {
        super.init()
        setupURLSession()
        setupNetworkMonitoring()
    }
    
    private func setupURLSession() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 0 // No timeout for WebSocket
        urlSession = URLSession(configuration: config, delegate: self, delegateQueue: nil)
    }

    private func setupNetworkMonitoring() {
        // Monitor network status changes (don't start monitoring here - it's handled centrally)
        networkReachability.$isConnected
            .receive(on: DispatchQueue.main)
            .sink { [weak self] isConnected in
                self?.handleNetworkStatusChange(isConnected: isConnected)
            }
            .store(in: &cancellables)
    }

    private func handleNetworkStatusChange(isConnected: Bool) {
        if isConnected {
            print("🌐 WebSocketManager: Network connectivity restored")
            // If we were disconnected due to network issues, attempt reconnection
            if case .disconnected(let reason) = connectionState,
               reason == .noNetworkConnection || reason == .networkError {
                print("🔄 WebSocketManager: Attempting reconnection after network restoration")
                attemptReconnection()
            }
        } else {
            print("🌐 WebSocketManager: Network connectivity lost")
            // If currently connected or connecting, disconnect due to network loss
            if connectionState != .disconnected(reason: .noNetworkConnection) {
                disconnect(reason: .noNetworkConnection)
            }
        }
    }
    
    func connect(to urlString: String? = nil, with config: FunASRConfiguration? = nil) {
        print("🌐 WebSocketManager: Connecting to WebSocket...")

        // Check network connectivity first (but allow unknown status to proceed)
        let networkStatus = networkReachability.networkStatus
        if case .disconnected = networkStatus {
            print("❌ WebSocketManager: No network connectivity available")
            DispatchQueue.main.async {
                self.connectionState = .disconnected(reason: .noNetworkConnection)
            }
            delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: .noNetworkConnection))
            return
        }

        if let urlString = urlString {
            self.serverURL = urlString
            print("🔗 WebSocketManager: Server URL set to: \(urlString)")
        }

        // Store FunASR configuration (adapted from JavaScript request object)
        if let config = config {
            self.funasrConfig = config
            print("⚙️ WebSocketManager: FunASR config set - Mode: \(config.mode), ITN: \(config.itn)")
        }

        guard let url = URL(string: serverURL) else {
            print("❌ WebSocketManager: Invalid URL: \(serverURL)")
            delegate?.webSocketManager(self, didEncounterError: WebSocketError.invalidURL)
            return
        }

        disconnect(reason: .userInitiated)

        DispatchQueue.main.async {
            self.connectionState = .connecting
        }

        isConfigurationSent = false
        webSocketTask = urlSession?.webSocketTask(with: url)
        webSocketTask?.resume()
        print("🚀 WebSocketManager: WebSocket task started")

        // Start connection timeout timer
        connectionTimeoutTimer = Timer.scheduledTimer(withTimeInterval: connectionTimeout, repeats: false) { [weak self] _ in
            guard let self = self, self.connectionState == .connecting else { return }

            self.webSocketTask?.cancel(with: .abnormalClosure, reason: nil)

            DispatchQueue.main.async {
                self.connectionState = .disconnected(reason: .connectionTimeout)
            }

            self.delegate?.webSocketManager(self, didEncounterError: WebSocketError.connectionTimeout)
            self.delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: .connectionTimeout))
        }

        sendFunASRConfiguration()
        startListening()
        delegate?.webSocketManager(self, didChangeConnectionState: .connecting)
    }
    
    func disconnect(reason: DisconnectionReason = .userInitiated) {
        // Store the disconnection reason for tracking
        disconnectionReason = reason
        isUserInitiatedDisconnection = (reason == .userInitiated)

        // Clean up timers first to prevent any race conditions
        reconnectTimer?.invalidate()
        reconnectTimer = nil
        connectionTimeoutTimer?.invalidate()
        connectionTimeoutTimer = nil
        reconnectAttempts = 0

        // Use appropriate close code based on reason
        let closeCode: URLSessionWebSocketTask.CloseCode = reason == .userInitiated ? .normalClosure : .abnormalClosure

        webSocketTask?.cancel(with: closeCode, reason: nil)
        webSocketTask = nil

        DispatchQueue.main.async {
            self.connectionState = .disconnected(reason: reason)
        }

        delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: reason))

        print("🔌 WebSocketManager: Disconnected with reason: \(reason)")
    }

    func forceDisconnect() {
        // Force disconnect for system errors without user intervention
        disconnect(reason: .networkError)
        print("⚠️ WebSocketManager: Force disconnected due to system error")
    }
    
    func sendAudioData(_ data: Data) {
        guard connectionState == .connected else { return }
        
        let message = URLSessionWebSocketTask.Message.data(data)
        webSocketTask?.send(message) { [weak self] error in
            if let error = error {
                self?.delegate?.webSocketManager(self!, didEncounterError: error)
            }
        }
    }
    
    private func startListening() {
        webSocketTask?.receive { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let message):
                self.handleMessage(message)
                self.startListening() // Continue listening
                
            case .failure(let error):
                self.handleError(error)
            }
        }
    }
    
    private func handleMessage(_ message: URLSessionWebSocketTask.Message) {
        switch message {
        case .string(let text):
            parseTranscriptionResponse(text)
            
        case .data(let data):
            if let text = String(data: data, encoding: .utf8) {
                parseTranscriptionResponse(text)
            }
            
        @unknown default:
            break
        }
    }
    
    /// Sends FunASR configuration JSON - adapted from JavaScript onOpen function
    /// Matches the request object structure from wsconnecter.js
    private func sendFunASRConfiguration() {
        guard let config = funasrConfig, !isConfigurationSent else {
            print("⚠️ WebSocketManager: No FunASR config to send or already sent")
            return
        }
        
        do {
            // Encode to JSON (equivalent to JSON.stringify(request) in JavaScript)
            let jsonData = try JSONEncoder().encode(config)
            let jsonString = String(data: jsonData, encoding: .utf8) ?? ""
            print("📋 WebSocketManager: Sending FunASR configuration: \(jsonString)")
            
            // Send as string message (equivalent to speechSokt.send(JSON.stringify(request)))
            let message = URLSessionWebSocketTask.Message.string(jsonString)
            
            webSocketTask?.send(message) { [weak self] error in
                if let error = error {
                    print("❌ WebSocketManager: Failed to send FunASR config: \(error)")
                    self?.delegate?.webSocketManager(self!, didEncounterError: error)
                } else {
                    print("✅ WebSocketManager: FunASR configuration sent successfully")
                    self?.isConfigurationSent = true
                    // Equivalent to console.log("连接成功") and stateHandle(0) in JavaScript
                }
            }
        } catch {
            print("❌ WebSocketManager: Failed to encode FunASR config: \(error)")
            delegate?.webSocketManager(self, didEncounterError: error)
        }
    }
    
    private func parseTranscriptionResponse(_ jsonString: String) {
        print("🔍 WebSocketManager: Parsing JSON response: \(jsonString)")

        guard let data = jsonString.data(using: .utf8) else {
            print("❌ WebSocketManager: Failed to convert JSON string to data")
            return
        }

        do {
            let response = try JSONDecoder().decode(TranscriptionResponse.self, from: data)
            print("✅ WebSocketManager: Successfully parsed response - Text: '\(response.text)', Mode: \(response.mode), Final: \(response.isFinal)")

            DispatchQueue.main.async {
                self.delegate?.webSocketManager(self, didReceiveTranscription: response)
            }
        } catch {
            print("❌ WebSocketManager: Failed to decode JSON response: \(error)")
            print("📋 WebSocketManager: Raw JSON: \(jsonString)")
            delegate?.webSocketManager(self, didEncounterError: error)
        }
    }
    
    private func handleError(_ error: Error) {
        // Don't process errors if user has already initiated disconnection
        if isUserInitiatedDisconnection {
            print("🚫 WebSocketManager: Ignoring error during user-initiated disconnection: \(error.localizedDescription)")
            return
        }

        // Classify the error to determine appropriate disconnection reason
        let reason = classifyError(error)
        disconnectionReason = reason

        print("❌ WebSocketManager: Error occurred - \(error.localizedDescription), classified as: \(reason)")

        delegate?.webSocketManager(self, didEncounterError: error)

        DispatchQueue.main.async {
            self.connectionState = .disconnected(reason: reason)
        }

        delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: reason))

        // Attempt reconnection based on error classification
        attemptReconnection()
    }

    internal func classifyError(_ error: Error) -> DisconnectionReason {
        // Check if it's a URLError (network-related)
        if let urlError = error as? URLError {
            switch urlError.code {
            case .notConnectedToInternet, .networkConnectionLost, .dataNotAllowed:
                return .networkError
            case .timedOut:
                return .connectionTimeout
            case .cannotFindHost, .cannotConnectToHost, .dnsLookupFailed:
                return .serverError
            case .badServerResponse, .badURL:
                return .protocolError
            default:
                return .networkError
            }
        }

        // Check if it's a WebSocket-specific error
        if let nsError = error as NSError? {
            switch nsError.domain {
            case NSURLErrorDomain:
                return .networkError
            case "NSPOSIXErrorDomain":
                // POSIX errors are usually network-related
                return .networkError
            default:
                break
            }
        }

        // Check error description for common patterns
        let errorDescription = error.localizedDescription.lowercased()
        if errorDescription.contains("network") || errorDescription.contains("connection") {
            return .networkError
        } else if errorDescription.contains("timeout") {
            return .connectionTimeout
        } else if errorDescription.contains("server") || errorDescription.contains("host") {
            return .serverError
        } else if errorDescription.contains("protocol") || errorDescription.contains("websocket") {
            return .protocolError
        }

        // Default to network error for unclassified errors
        return .networkError
    }
    
    internal func shouldAttemptReconnection(for reason: DisconnectionReason) -> Bool {
        switch reason {
        case .userInitiated:
            return false // Never reconnect for user-initiated disconnections
        case .networkError, .serverError, .connectionTimeout, .protocolError:
            return true // Reconnect for system errors
        case .noNetworkConnection:
            return false // Don't reconnect when there's no network - will be handled by network monitoring
        case .unknown:
            return false // Don't reconnect for unknown reasons to be safe
        }
    }

    private func attemptReconnection() {
        // Check if we should attempt reconnection based on the disconnection reason
        guard shouldAttemptReconnection(for: disconnectionReason) else {
            print("🚫 WebSocketManager: Skipping reconnection for reason: \(disconnectionReason)")
            return
        }

        guard reconnectAttempts < maxReconnectAttempts else {
            delegate?.webSocketManager(self, didEncounterError: WebSocketError.maxReconnectAttemptsReached)
            return
        }

        reconnectAttempts += 1

        DispatchQueue.main.async {
            self.connectionState = .reconnecting(attempt: self.reconnectAttempts, maxAttempts: self.maxReconnectAttempts)
        }

        delegate?.webSocketManager(self, didChangeConnectionState: .reconnecting(attempt: self.reconnectAttempts, maxAttempts: self.maxReconnectAttempts))

        // Use different backoff strategies based on error type
        let delay = calculateReconnectionDelay(for: disconnectionReason, attempt: reconnectAttempts)

        print("🔄 WebSocketManager: Attempting reconnection \(reconnectAttempts)/\(maxReconnectAttempts) in \(delay)s for reason: \(disconnectionReason)")

        reconnectTimer = Timer.scheduledTimer(withTimeInterval: delay, repeats: false) { [weak self] _ in
            self?.connect()
        }
    }

    internal func calculateReconnectionDelay(for reason: DisconnectionReason, attempt: Int) -> TimeInterval {
        switch reason {
        case .networkError, .connectionTimeout:
            // Exponential backoff for network issues (2s, 4s, 8s, 16s, 32s)
            return reconnectDelay * pow(2.0, Double(attempt - 1))
        case .serverError:
            // Linear backoff for server issues (5s intervals)
            return 5.0 * Double(attempt)
        case .protocolError:
            // Single retry after 3s delay for protocol errors
            return 3.0
        default:
            return reconnectDelay
        }
    }
    
    deinit {
        disconnect(reason: .userInitiated)
    }


}



// MARK: - URLSessionWebSocketDelegate
extension WebSocketManager: URLSessionWebSocketDelegate {
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didOpenWithProtocol protocol: String?) {
        // Clear connection timeout timer
        connectionTimeoutTimer?.invalidate()
        connectionTimeoutTimer = nil

        DispatchQueue.main.async {
            self.connectionState = .connected
        }

        reconnectAttempts = 0
        delegate?.webSocketManager(self, didChangeConnectionState: .connected)
    }
    
    func urlSession(_ session: URLSession, webSocketTask: URLSessionWebSocketTask, didCloseWith closeCode: URLSessionWebSocketTask.CloseCode, reason: Data?) {
        // Analyze close code to determine disconnection reason
        let analyzedReason = analyzeCloseCode(closeCode, reasonData: reason)

        // Only override the disconnection reason if it wasn't already set by user action
        if !isUserInitiatedDisconnection {
            disconnectionReason = analyzedReason
        }

        print("🔌 WebSocketManager: Connection closed with code: \(closeCode.rawValue), reason: \(disconnectionReason)")

        DispatchQueue.main.async {
            self.connectionState = .disconnected(reason: self.disconnectionReason)
        }

        delegate?.webSocketManager(self, didChangeConnectionState: .disconnected(reason: disconnectionReason))

        // Only attempt reconnection if it's appropriate for this disconnection reason
        // Skip reconnection for user-initiated disconnections
        if !isUserInitiatedDisconnection {
            attemptReconnection()
        } else {
            print("🚫 WebSocketManager: Skipping reconnection - user initiated disconnection")
        }
    }

    private func analyzeCloseCode(_ closeCode: URLSessionWebSocketTask.CloseCode, reasonData: Data?) -> DisconnectionReason {
        switch closeCode {
        case .normalClosure:
            return .userInitiated
        case .goingAway:
            return .userInitiated
        case .protocolError:
            return .protocolError
        case .unsupportedData:
            return .protocolError
        case .noStatusReceived:
            return .networkError
        case .abnormalClosure:
            return .networkError
        case .invalidFramePayloadData:
            return .protocolError
        case .policyViolation:
            return .serverError
        case .messageTooBig:
            return .protocolError
        case .internalServerError:
            return .serverError
        case .invalid:
            return .protocolError
        case .mandatoryExtensionMissing:
            return .protocolError
        case .tlsHandshakeFailure:
            return .networkError
        @unknown default:
            return .unknown
        }
    }
}

// MARK: - Error Types and Disconnection Reasons
enum DisconnectionReason: Equatable {
    case userInitiated          // User stopped recording
    case networkError          // Network connectivity issues
    case serverError           // Server-side problems
    case connectionTimeout     // Connection establishment timeout
    case protocolError         // WebSocket protocol violations
    case noNetworkConnection   // No internet connectivity available
    case unknown              // Fallback for unclassified errors
}

struct WebSocketErrorContext {
    let error: WebSocketError
    let timestamp: Date
    let connectionAttempt: Int
    let shouldRetry: Bool
    let userMessage: String
    
    init(error: WebSocketError, connectionAttempt: Int = 0) {
        self.error = error
        self.timestamp = Date()
        self.connectionAttempt = connectionAttempt
        self.shouldRetry = error.shouldAttemptReconnection
        self.userMessage = error.errorDescription ?? "Unknown error occurred"
    }
}

enum WebSocketError: LocalizedError {
    case invalidURL
    case connectionFailed(reason: String)
    case connectionTimeout
    case networkUnavailable
    case serverUnreachable
    case protocolError(String)
    case maxReconnectAttemptsReached
    case userCancelled

    var shouldAttemptReconnection: Bool {
        switch self {
        case .userCancelled:
            return false
        case .invalidURL:
            return false
        case .maxReconnectAttemptsReached:
            return false
        default:
            return true
        }
    }

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid server URL. Please check your connection settings."
        case .connectionFailed(let reason):
            return "Connection failed: \(reason)"
        case .connectionTimeout:
            return "Connection timeout. Please check your network and try again."
        case .networkUnavailable:
            return "Network unavailable. Please check your internet connection."
        case .serverUnreachable:
            return "Server unreachable. Please try again later."
        case .protocolError(let details):
            return "Communication error: \(details). Please restart the connection."
        case .maxReconnectAttemptsReached:
            return "Unable to reconnect after multiple attempts. Please check your connection and try again."
        case .userCancelled:
            return nil // No error message for user-cancelled operations
        }
    }

    var userFriendlyMessage: String {
        switch self {
        case .invalidURL:
            return "Invalid server URL"
        case .connectionFailed:
            return "Connection failed"
        case .connectionTimeout:
            return "Connection timeout"
        case .networkUnavailable:
            return "Network unavailable"
        case .serverUnreachable:
            return "Server unavailable"
        case .protocolError:
            return "Communication error"
        case .maxReconnectAttemptsReached:
            return "Unable to reconnect"
        case .userCancelled:
            return "Connection cancelled"
        }
    }
}
