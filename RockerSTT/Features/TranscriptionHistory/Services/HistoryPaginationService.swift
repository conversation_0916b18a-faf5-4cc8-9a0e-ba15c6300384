//
//  HistoryPaginationService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import Foundation
import CoreData
import Combine

/// Service for handling pagination and lazy loading of history sessions
class HistoryPaginationService: ObservableObject {
    
    // MARK: - Properties
    
    @Published var sessions: [HistorySession] = []
    @Published var isLoading = false
    @Published var isRefreshing = false
    @Published var hasMoreData = true
    @Published var currentPage = 0
    @Published var error: HistoryError?
    
    private let pageSize: Int
    private let storageService: HistoryStorageService
    private var cancellables = Set<AnyCancellable>()
    private var totalCount: Int = 0
    
    // MARK: - Performance Metrics
    
    @Published var loadTime: TimeInterval = 0
    @Published var memoryUsage: Int64 = 0
    
    // MARK: - Initialization
    
    init(pageSize: Int = 20, storageService: HistoryStorageService = HistoryStorageService()) {
        self.pageSize = pageSize
        self.storageService = storageService
        
        setupMemoryMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// Loads the first page of sessions
    @MainActor
    func loadInitialSessions(for tab: HistoryTab = .recents) async {
        guard !isLoading else { return }
        
        isLoading = true
        currentPage = 0
        sessions.removeAll()
        hasMoreData = true
        error = nil
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        do {
            let fetchedSessions = try await fetchSessions(page: currentPage, tab: tab)
            sessions = fetchedSessions
            
            // Update pagination state
            hasMoreData = fetchedSessions.count == pageSize
            currentPage += 1
            
            // Update total count for better pagination
            totalCount = try await getTotalCount(for: tab)
            
            loadTime = CFAbsoluteTimeGetCurrent() - startTime
            
            print("📄 HistoryPaginationService: Loaded \(fetchedSessions.count) sessions in \(String(format: "%.3f", loadTime))s")
            
        } catch {
            self.error = error as? HistoryError ?? .storageFailure("Failed to load initial sessions")
            print("❌ HistoryPaginationService: Failed to load initial sessions - \(error)")
        }
        
        isLoading = false
    }
    
    /// Loads the next page of sessions
    @MainActor
    func loadNextPage(for tab: HistoryTab = .recents) async {
        guard !isLoading && hasMoreData else { return }
        
        isLoading = true
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        do {
            let fetchedSessions = try await fetchSessions(page: currentPage, tab: tab)
            
            // Append new sessions to existing ones
            sessions.append(contentsOf: fetchedSessions)
            
            // Update pagination state
            hasMoreData = fetchedSessions.count == pageSize && sessions.count < totalCount
            currentPage += 1
            
            loadTime = CFAbsoluteTimeGetCurrent() - startTime
            
            print("📄 HistoryPaginationService: Loaded page \(currentPage) with \(fetchedSessions.count) sessions in \(String(format: "%.3f", loadTime))s")
            
        } catch {
            self.error = error as? HistoryError ?? .storageFailure("Failed to load next page")
            print("❌ HistoryPaginationService: Failed to load next page - \(error)")
        }
        
        isLoading = false
    }
    
    /// Refreshes the current data without clearing existing sessions first
    @MainActor
    func refresh(for tab: HistoryTab = .recents) async {
        print("🔄 HistoryPaginationService.refresh() called for tab: \(tab)")
        guard !isLoading else {
            print("🔄 HistoryPaginationService.refresh() skipped - already loading")
            return
        }

        print("🔄 HistoryPaginationService.refresh() starting - setting isRefreshing = true")
        isLoading = true
        isRefreshing = true
        let previousSessionCount = sessions.count
        currentPage = 0
        hasMoreData = true
        error = nil

        let startTime = CFAbsoluteTimeGetCurrent()

        do {
            // Fetch at least as many sessions as we had before, plus one page extra to catch new sessions
            let sessionsToFetch = max(previousSessionCount + pageSize, pageSize)
            let fetchedSessions = try await fetchSessionsWithLimit(limit: sessionsToFetch, tab: tab)

            // Replace sessions only after successful fetch
            sessions = fetchedSessions

            // Update pagination state based on how many we fetched vs requested
            hasMoreData = fetchedSessions.count >= sessionsToFetch
            currentPage = (fetchedSessions.count / pageSize) + 1

            // Update total count for better pagination
            totalCount = try await getTotalCount(for: tab)

            loadTime = CFAbsoluteTimeGetCurrent() - startTime

            print("🔄 HistoryPaginationService: Refreshed \(fetchedSessions.count) sessions (was \(previousSessionCount)) in \(String(format: "%.3f", loadTime))s")

            // Debug: Print first few session titles and dates
            for (index, session) in fetchedSessions.prefix(3).enumerated() {
                let title = session.title ?? "Untitled"
                let updatedAt = session.updatedAt?.description ?? "No date"
                print("🔄   Session \(index + 1): '\(title)' updated at \(updatedAt)")
            }

        } catch {
            self.error = error as? HistoryError ?? .storageFailure("Failed to refresh sessions")
            print("❌ HistoryPaginationService: Failed to refresh sessions - \(error)")
        }

        print("🔄 HistoryPaginationService.refresh() completed - setting isRefreshing = false")
        isLoading = false
        isRefreshing = false
    }
    
    /// Clears all loaded data
    func clearData() {
        sessions.removeAll()
        currentPage = 0
        hasMoreData = true
        totalCount = 0
        error = nil
    }
    
    // MARK: - Private Methods

    /// Fetches sessions with a specific limit (for refresh operations)
    private func fetchSessionsWithLimit(limit: Int, tab: HistoryTab) async throws -> [HistorySession] {
        return try await withCheckedThrowingContinuation { continuation in
            // Use main context for immediate refresh to see latest changes
            let context = CoreDataStack.shared.mainContext

            context.perform {
                do {
                    let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()

                    // Configure fetch request for performance
                    request.fetchLimit = limit
                    request.fetchOffset = 0  // Always start from the beginning for refresh
                    request.returnsObjectsAsFaults = false
                    request.includesSubentities = false

                    // Add predicates based on tab
                    request.predicate = self.createPredicate(for: tab)

                    // Add sorting for consistent pagination
                    request.sortDescriptors = self.createSortDescriptors(for: tab)

                    // Optimize for performance
                    request.relationshipKeyPathsForPrefetching = ["entries"]
                    request.propertiesToFetch = ["id", "title", "createdAt", "updatedAt", "isFavourite", "isSaved"]

                    let sessions = try context.fetch(request)
                    continuation.resume(returning: sessions)

                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    private func fetchSessions(page: Int, tab: HistoryTab) async throws -> [HistorySession] {
        return try await withCheckedThrowingContinuation { continuation in
            // Use main context for immediate refresh to see latest changes
            let context = CoreDataStack.shared.mainContext

            context.perform {
                do {
                    let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
                    
                    // Configure fetch request for performance
                    request.fetchLimit = self.pageSize
                    request.fetchOffset = page * self.pageSize
                    request.returnsObjectsAsFaults = false
                    request.includesSubentities = false
                    
                    // Add predicates based on tab
                    request.predicate = self.createPredicate(for: tab)
                    
                    // Add sorting for consistent pagination
                    request.sortDescriptors = self.createSortDescriptors(for: tab)
                    
                    // Optimize for performance
                    request.relationshipKeyPathsForPrefetching = ["entries"]
                    request.propertiesToFetch = ["id", "title", "createdAt", "isFavourite", "isSaved"]
                    
                    let sessions = try context.fetch(request)
                    
                    // Convert to main context objects
                    let mainContextSessions = sessions.compactMap { session in
                        try? CoreDataStack.shared.mainContext.existingObject(with: session.objectID) as? HistorySession
                    }
                    
                    continuation.resume(returning: mainContextSessions)
                    
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func getTotalCount(for tab: HistoryTab) async throws -> Int {
        return try await withCheckedThrowingContinuation { continuation in
            // Use main context for immediate refresh to see latest changes
            let context = CoreDataStack.shared.mainContext

            context.perform {
                do {
                    let request: NSFetchRequest<HistorySession> = HistorySession.fetchRequest()
                    request.predicate = self.createPredicate(for: tab)
                    request.includesSubentities = false
                    
                    let count = try context.count(for: request)
                    continuation.resume(returning: count)
                    
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }
    
    private func createPredicate(for tab: HistoryTab) -> NSPredicate? {
        switch tab {
        case .recents:
            return nil // Show all sessions
        case .favorites:
            return NSPredicate(format: "isFavourite == YES")
        case .saved:
            return NSPredicate(format: "isSaved == YES")
        }
    }
    
    private func createSortDescriptors(for tab: HistoryTab) -> [NSSortDescriptor] {
        switch tab {
        case .recents:
            // Sort by updatedAt so newly saved sessions appear at the top
            return [NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)]
        case .favorites:
            return [
                NSSortDescriptor(keyPath: \HistorySession.isFavourite, ascending: false),
                NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
            ]
        case .saved:
            return [
                NSSortDescriptor(keyPath: \HistorySession.isSaved, ascending: false),
                NSSortDescriptor(keyPath: \HistorySession.updatedAt, ascending: false)
            ]
        }
    }
    
    // MARK: - Memory Monitoring
    
    private func setupMemoryMonitoring() {
        Timer.publish(every: 5.0, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateMemoryUsage()
            }
            .store(in: &cancellables)
    }
    
    private func updateMemoryUsage() {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            memoryUsage = Int64(info.resident_size)
        }
    }
    
    // MARK: - Performance Optimization
    
    /// Optimizes memory usage by releasing unused sessions
    func optimizeMemoryUsage() {
        // Keep only the most recent sessions if we have too many
        let maxSessionsInMemory = 100
        
        if sessions.count > maxSessionsInMemory {
            let sessionsToKeep = Array(sessions.prefix(maxSessionsInMemory))
            sessions = sessionsToKeep
            
            // Update pagination state
            currentPage = maxSessionsInMemory / pageSize
            hasMoreData = true
            
            print("🧹 HistoryPaginationService: Optimized memory usage, keeping \(sessionsToKeep.count) sessions")
        }
    }
    
    /// Preloads next page if user is near the end
    @MainActor
    func preloadIfNeeded(currentIndex: Int, tab: HistoryTab) async {
        let threshold = sessions.count - 5 // Preload when 5 items from end
        
        if currentIndex >= threshold && hasMoreData && !isLoading {
            await loadNextPage(for: tab)
        }
    }
}

// MARK: - Performance Metrics Extension

extension HistoryPaginationService {
    
    /// Returns formatted memory usage string
    var formattedMemoryUsage: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .memory
        return formatter.string(fromByteCount: memoryUsage)
    }
    
    /// Returns performance summary
    var performanceSummary: String {
        return """
        Sessions: \(sessions.count)/\(totalCount)
        Load Time: \(String(format: "%.3f", loadTime))s
        Memory: \(formattedMemoryUsage)
        Page: \(currentPage)
        """
    }
}
