//
//  ExportOptionsView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI

/// View for configuring export options and initiating export
struct ExportOptionsView: View {
    
    // MARK: - Properties
    
    let sessions: [HistorySession]
    @StateObject private var exportService = HistoryExportService()
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedFormat: HistoryExportService.ExportFormat = .plainText
    @State private var includeTranslations = true
    @State private var includeTimestamps = true
    @State private var includeMetadata = true
    @State private var includeEmotions = false
    @State private var separateLanguages = false
    
    @State private var showingShareSheet = false
    @State private var exportedData: Data?
    @State private var exportedFilename: String = ""
    @State private var showingErrorAlert = false
    
    // Design constants
    private let sectionSpacing: CGFloat = 24
    private let optionSpacing: CGFloat = 16
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: sectionSpacing) {
                    // Export Summary
                    ExportSummaryCard(sessions: sessions)
                    
                    // Format Selection
                    FormatSelectionSection(selectedFormat: $selectedFormat)
                    
                    // Export Options
                    ExportOptionsSection(
                        includeTranslations: $includeTranslations,
                        includeTimestamps: $includeTimestamps,
                        includeMetadata: $includeMetadata,
                        includeEmotions: $includeEmotions,
                        separateLanguages: $separateLanguages
                    )
                    
                    // Export Button
                    ExportButtonSection(
                        isExporting: exportService.isExporting,
                        exportProgress: exportService.exportProgress,
                        onExport: performExport
                    )
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .navigationTitle("Export Options")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Export Error", isPresented: $showingErrorAlert) {
                Button("OK") { }
            } message: {
                Text(exportService.lastExportError?.localizedDescription ?? "An unknown error occurred during export.")
            }
            .sheet(isPresented: $showingShareSheet) {
                if let data = exportedData {
                    ShareSheet(activityItems: [ExportFileWrapper(data: data, filename: exportedFilename)])
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func performExport() {
        let options = HistoryExportService.ExportOptions(
            includeTranslations: includeTranslations,
            includeTimestamps: includeTimestamps,
            includeMetadata: includeMetadata,
            includeEmotions: includeEmotions,
            separateLanguages: separateLanguages
        )
        
        Task {
            do {
                let data = try exportService.exportSessions(sessions, format: selectedFormat, options: options)
                let filename = exportService.generateFilename(for: sessions, format: selectedFormat)
                
                await MainActor.run {
                    exportedData = data
                    exportedFilename = filename
                    showingShareSheet = true
                }
            } catch {
                await MainActor.run {
                    showingErrorAlert = true
                }
            }
        }
    }
}

// MARK: - Export Summary Card

private struct ExportSummaryCard: View {
    let sessions: [HistorySession]
    
    private var totalEntries: Int {
        sessions.reduce(0) { $0 + Int($1.entryCount) }
    }

    private var totalWordCount: Int {
        sessions.reduce(0) { $0 + Int($1.wordCount) }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Export Summary")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 20) {
//                SummaryItem(
//                    title: "Sessions",
//                    value: "\(sessions.count)",
//                    icon: "folder"
//                )
                
                SummaryItem(
                    title: "Sentences",
                    value: "\(totalEntries)",
                    icon: "text.bubble"
                )
                
                SummaryItem(
                    title: "Words",
                    value: "\(totalWordCount)",
                    icon: "textformat"
                )
            }
        }
        .padding(20)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Summary Item

private struct SummaryItem: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Format Selection Section

private struct FormatSelectionSection: View {
    @Binding var selectedFormat: HistoryExportService.ExportFormat
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Export Format")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(HistoryExportService.ExportFormat.allCases, id: \.self) { format in
                    FormatCard(
                        format: format,
                        isSelected: selectedFormat == format,
                        onTap: {
                            selectedFormat = format
                        }
                    )
                }
            }
        }
    }
}

// MARK: - Format Card

private struct FormatCard: View {
    let format: HistoryExportService.ExportFormat
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                Image(systemName: formatIcon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : .blue)
                
                Text(format.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : .primary)
                
                Text(".\(format.fileExtension)")
                    .font(.caption2)
                    .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(isSelected ? Color.blue : Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var formatIcon: String {
        switch format {
        case .plainText: return "doc.text"
        case .markdown: return "doc.richtext"
        case .json: return "doc.badge.gearshape"
        case .csv: return "tablecells"
        }
    }
}

// MARK: - Export Options Section

private struct ExportOptionsSection: View {
    @Binding var includeTranslations: Bool
    @Binding var includeTimestamps: Bool
    @Binding var includeMetadata: Bool
    @Binding var includeEmotions: Bool
    @Binding var separateLanguages: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Export Options")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                OptionToggle(
                    title: "Include Translations",
                    description: "Export translated text alongside original content",
                    isOn: $includeTranslations,
                    icon: "globe"
                )
                
                OptionToggle(
                    title: "Include Timestamps",
                    description: "Add time information for each entry",
                    isOn: $includeTimestamps,
                    icon: "clock"
                )
                
                OptionToggle(
                    title: "Include Metadata",
                    description: "Export note information and statistics",
                    isOn: $includeMetadata,
                    icon: "info.circle"
                )
                
//                OptionToggle(
//                    title: "Include Emotions",
//                    description: "Export detected emotional context",
//                    isOn: $includeEmotions,
//                    icon: "face.smiling"
//                )
                
                OptionToggle(
                    title: "Separate Languages",
                    description: "Group entries by detected language",
                    isOn: $separateLanguages,
                    icon: "text.book.closed"
                )
            }
        }
    }
}

// MARK: - Option Toggle

private struct OptionToggle: View {
    let title: String
    let description: String
    @Binding var isOn: Bool
    let icon: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .labelsHidden()
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Export Button Section

private struct ExportButtonSection: View {
    let isExporting: Bool
    let exportProgress: Double
    let onExport: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            if isExporting {
                VStack(spacing: 12) {
                    ProgressView(value: exportProgress)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    Text("Exporting... \(Int(exportProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Button(action: onExport) {
                HStack {
                    if isExporting {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "square.and.arrow.up")
                            .font(.title3)
                    }
                    
                    Text(isExporting ? "Exporting..." : "Export")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(isExporting ? Color.gray : Color.blue)
                .cornerRadius(12)
            }
            .disabled(isExporting)
        }
    }
}

// MARK: - Export File Wrapper

class ExportFileWrapper: NSObject, UIActivityItemSource {
    let data: Data
    let filename: String
    
    init(data: Data, filename: String) {
        self.data = data
        self.filename = filename
        super.init()
    }
    
    func activityViewControllerPlaceholderItem(_ activityViewController: UIActivityViewController) -> Any {
        return data
    }
    
    func activityViewController(_ activityViewController: UIActivityViewController, itemForActivityType activityType: UIActivity.ActivityType?) -> Any? {
        return data
    }
    
    func activityViewController(_ activityViewController: UIActivityViewController, subjectForActivityType activityType: UIActivity.ActivityType?) -> String {
        return filename
    }
    
    func activityViewController(_ activityViewController: UIActivityViewController, dataTypeIdentifierForActivityType activityType: UIActivity.ActivityType?) -> String {
        return "public.data"
    }
}

// MARK: - Preview

#Preview {
    struct PreviewWrapper: View {
        @State private var sampleSessions: [HistorySession] = {
            let session1 = HistorySession()
            session1.title = "Meeting Notes"
            session1.entryCount = 25
            
            let session2 = HistorySession()
            session2.title = "Interview Recording"
            session2.entryCount = 18
            
            return [session1, session2]
        }()
        
        var body: some View {
            ExportOptionsView(sessions: sampleSessions)
        }
    }
    
    return PreviewWrapper()
}
