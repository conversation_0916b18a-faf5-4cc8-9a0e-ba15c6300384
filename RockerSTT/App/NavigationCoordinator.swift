//
//  NavigationCoordinator.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI
import Foundation

/// Coordinates navigation throughout the app
@MainActor
class NavigationCoordinator: ObservableObject {
    
    // MARK: - Published Properties
    
    /// Currently selected tab
    @Published var selectedTab: AppTab = .record
    
    /// Navigation path for the history tab
    @Published var historyNavigationPath = NavigationPath()
    
    /// Navigation path for the settings tab
    @Published var settingsNavigationPath = NavigationPath()
    
    /// Current session being viewed in detail
    @Published var currentDetailSession: HistorySession?
    
    /// Whether to show the search view
    @Published var showingSearch = false
    
    /// Whether to show the export options
    @Published var showingExport = false
    
    /// Badge count for history tab
    @Published var historyBadgeCount: Int = 0

    /// Whether to show the dropdown menu
    @Published var showingDropdownMenu = false
    
    // MARK: - Navigation Methods
    
    /// Navigate to a specific tab
    func navigateToTab(_ tab: AppTab) {
        selectedTab = tab
    }
    
    /// Navigate to history tab and show a specific session
    func navigateToSession(_ session: HistorySession) {
        selectedTab = .history
        currentDetailSession = session
        historyNavigationPath.append(NavigationDestination.sessionDetail(session))
    }
    
    /// Navigate to history tab and show search
    func navigateToHistorySearch() {
        selectedTab = .history
        showingSearch = true
    }
    
    /// Navigate to settings with a specific section
    func navigateToSettings(section: SettingsSection? = nil) {
        selectedTab = .settings
        if let section = section {
            settingsNavigationPath.append(NavigationDestination.settingsSection(section))
        }
    }
    
    /// Navigate back in history
    func navigateBackInHistory() {
        if !historyNavigationPath.isEmpty {
            historyNavigationPath.removeLast()
        }
    }
    
    /// Navigate back in settings
    func navigateBackInSettings() {
        if !settingsNavigationPath.isEmpty {
            settingsNavigationPath.removeLast()
        }
    }
    
    /// Clear all navigation paths
    func clearAllPaths() {
        historyNavigationPath = NavigationPath()
        settingsNavigationPath = NavigationPath()
        currentDetailSession = nil
        showingSearch = false
        showingExport = false
    }
    
    /// Handle deep link navigation
    func handleDeepLink(_ url: URL) {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let host = components.host else { return }
        
        switch host {
        case "history":
            handleHistoryDeepLink(components)
        case "settings":
            handleSettingsDeepLink(components)
        case "record":
            selectedTab = .record
        default:
            break
        }
    }
    
    /// Update badge count for history tab
    func updateHistoryBadgeCount(_ count: Int) {
        historyBadgeCount = count
    }

    /// Debug method to check current state
    func debugCurrentState() {
        print("NavigationCoordinator Debug State:")
        print("- Selected Tab: \(selectedTab)")
        print("- Showing Dropdown Menu: \(showingDropdownMenu)")
        print("- History Navigation Depth: \(historyNavigationDepth)")
        print("- Settings Navigation Depth: \(settingsNavigationDepth)")
        print("- Current Detail Session: \(currentDetailSession?.title ?? "None")")
    }
    
    // MARK: - Private Methods
    
    private func handleHistoryDeepLink(_ components: URLComponents) {
        selectedTab = .history
        
        // Parse path components
        let pathComponents = components.path.components(separatedBy: "/").filter { !$0.isEmpty }
        
        if pathComponents.isEmpty {
            // Just navigate to history
            return
        }
        
        switch pathComponents[0] {
        case "search":
            showingSearch = true
        case "session":
            if pathComponents.count > 1, let sessionId = UUID(uuidString: pathComponents[1]) {
                // Find and navigate to specific session
                navigateToSessionById(sessionId)
            }
        case "export":
            showingExport = true
        default:
            break
        }
    }
    
    private func handleSettingsDeepLink(_ components: URLComponents) {
        selectedTab = .settings
        
        let pathComponents = components.path.components(separatedBy: "/").filter { !$0.isEmpty }
        
        if pathComponents.isEmpty {
            return
        }
        
        switch pathComponents[0] {
        case "language":
            settingsNavigationPath.append(NavigationDestination.settingsSection(.language))
        case "smart-merging":
            settingsNavigationPath.append(NavigationDestination.settingsSection(.smartMerging))
        case "developer":
            settingsNavigationPath.append(NavigationDestination.settingsSection(.developer))
        default:
            break
        }
    }
    
    private func navigateToSessionById(_ sessionId: UUID) {
        // This would typically fetch the session from storage
        // For now, we'll just set up the navigation
        historyNavigationPath.append(NavigationDestination.sessionById(sessionId))
    }
}

// MARK: - App Tabs

enum AppTab: Int, CaseIterable {
    case record = 0
    case history = 1
    case settings = 2
    
    var title: String {
        switch self {
        case .record: return "Record"
        case .history: return "History"
        case .settings: return "Settings"
        }
    }
    
    var iconName: String {
        switch self {
        case .record: return "mic"
        case .history: return "clock.arrow.circlepath"
        case .settings: return "gear"
        }
    }
    
    var selectedIconName: String {
        switch self {
        case .record: return "mic.fill"
        case .history: return "clock.arrow.circlepath"
        case .settings: return "gear"
        }
    }
}

// MARK: - Navigation Destinations

enum NavigationDestination: Hashable {
    case sessionDetail(HistorySession)
    case sessionById(UUID)
    case settingsSection(SettingsSection)
    case historySearch
    case exportOptions
    
    func hash(into hasher: inout Hasher) {
        switch self {
        case .sessionDetail(let session):
            hasher.combine("sessionDetail")
            hasher.combine(session.id)
        case .sessionById(let id):
            hasher.combine("sessionById")
            hasher.combine(id)
        case .settingsSection(let section):
            hasher.combine("settingsSection")
            hasher.combine(section)
        case .historySearch:
            hasher.combine("historySearch")
        case .exportOptions:
            hasher.combine("exportOptions")
        }
    }
    
    static func == (lhs: NavigationDestination, rhs: NavigationDestination) -> Bool {
        switch (lhs, rhs) {
        case (.sessionDetail(let lhsSession), .sessionDetail(let rhsSession)):
            return lhsSession.id == rhsSession.id
        case (.sessionById(let lhsId), .sessionById(let rhsId)):
            return lhsId == rhsId
        case (.settingsSection(let lhsSection), .settingsSection(let rhsSection)):
            return lhsSection == rhsSection
        case (.historySearch, .historySearch),
             (.exportOptions, .exportOptions):
            return true
        default:
            return false
        }
    }
}

// MARK: - Settings Sections

enum SettingsSection: String, CaseIterable {
    case language = "language"
    case smartMerging = "smart-merging"
    case developer = "developer"
    case about = "about"
    
    var title: String {
        switch self {
        case .language: return "Language Settings"
        case .smartMerging: return "Smart Word Merging"
        case .developer: return "Developer Tools"
        case .about: return "About"
        }
    }
    
    var iconName: String {
        switch self {
        case .language: return "globe"
        case .smartMerging: return "wand.and.rays"
        case .developer: return "hammer"
        case .about: return "info.circle"
        }
    }
}

// MARK: - Deep Link URLs

extension NavigationCoordinator {
    
    /// Generate deep link URL for a session
    static func sessionDeepLink(sessionId: UUID) -> URL? {
        return URL(string: "rockerstt://history/session/\(sessionId.uuidString)")
    }
    
    /// Generate deep link URL for history search
    static func historySearchDeepLink() -> URL? {
        return URL(string: "rockerstt://history/search")
    }
    
    /// Generate deep link URL for settings section
    static func settingsDeepLink(section: SettingsSection) -> URL? {
        return URL(string: "rockerstt://settings/\(section.rawValue)")
    }
}

// MARK: - Navigation Helpers

extension NavigationCoordinator {
    
    /// Check if currently viewing a specific session
    func isViewingSession(_ session: HistorySession) -> Bool {
        return currentDetailSession?.id == session.id
    }
    
    /// Check if currently in a specific tab
    func isInTab(_ tab: AppTab) -> Bool {
        return selectedTab == tab
    }
    
    /// Get the current navigation depth for history
    var historyNavigationDepth: Int {
        return historyNavigationPath.count
    }
    
    /// Get the current navigation depth for settings
    var settingsNavigationDepth: Int {
        return settingsNavigationPath.count
    }
}

// MARK: - Notification Names

extension Notification.Name {
    static let navigateToSession = Notification.Name("navigateToSession")
    static let navigateToHistorySearch = Notification.Name("navigateToHistorySearch")
    static let navigateToSettings = Notification.Name("navigateToSettings")
}
