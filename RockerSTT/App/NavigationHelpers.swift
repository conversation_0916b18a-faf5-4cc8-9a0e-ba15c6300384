//
//  NavigationHelpers.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025-07-21.
//

import SwiftUI
import Foundation

/// Helper functions for navigation throughout the app
struct NavigationHelpers {
    
    /// Navigate to a specific session from anywhere in the app
    static func navigateToSession(_ session: HistorySession) {
        NotificationCenter.default.post(
            name: .navigateToSession,
            object: session
        )
    }
    
    /// Navigate to history search from anywhere in the app
    static func navigateToHistorySearch() {
        NotificationCenter.default.post(
            name: .navigateToHistorySearch,
            object: nil
        )
    }
    
    /// Navigate to settings with optional section
    static func navigateToSettings(section: SettingsSection? = nil) {
        NotificationCenter.default.post(
            name: .navigateToSettings,
            object: section
        )
    }
    
    /// Share a session using the system share sheet
    static func shareSession(_ session: HistorySession, from view: UIView) {
        guard let text = session.fullTranscriptionText else { return }
        
        let activityViewController = UIActivityViewController(
            activityItems: [text],
            applicationActivities: nil
        )
        
        // For iPad
        if let popover = activityViewController.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = view.bounds
        }
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(activityViewController, animated: true)
        }
    }
    
    /// Open URL in external browser
    static func openURL(_ url: URL) {
        UIApplication.shared.open(url)
    }
    
    /// Generate shareable link for a session
    @MainActor
    static func generateShareableLink(for session: HistorySession) -> URL? {
        return NavigationCoordinator.sessionDeepLink(sessionId: session.id ?? UUID())
    }
}

// MARK: - SwiftUI Environment Integration

/// Environment key for navigation coordinator
struct NavigationCoordinatorKey: EnvironmentKey {
    static let defaultValue: NavigationCoordinator? = nil
}

extension EnvironmentValues {
    var navigationCoordinator: NavigationCoordinator? {
        get { self[NavigationCoordinatorKey.self] }
        set { self[NavigationCoordinatorKey.self] = newValue }
    }
}

// MARK: - View Extensions

extension View {
    
    /// Add navigation coordinator to environment
    func withNavigationCoordinator(_ coordinator: NavigationCoordinator) -> some View {
        self.environment(\.navigationCoordinator, coordinator)
    }
    
    /// Navigate to session using environment coordinator
    func navigateToSession(_ session: HistorySession) -> some View {
        self.onTapGesture {
            NavigationHelpers.navigateToSession(session)
        }
    }
    

}

// MARK: - Quick Navigation Menu










